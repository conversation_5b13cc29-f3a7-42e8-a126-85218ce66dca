<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comment System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .test-results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>Comment System Test Page</h1>
    
    <div class="test-section">
        <h2>Database Connection Test</h2>
        <button onclick="testDatabaseConnection()">Test Database Connection</button>
        <div id="db-test-result"></div>
    </div>

    <div class="test-section">
        <h2>Comment Functionality Test</h2>
        <button onclick="testCommentInsertion()">Test Comment Insertion</button>
        <button onclick="testCommentRetrieval()">Test Comment Retrieval</button>
        <div id="comment-test-result"></div>
    </div>

    <div class="test-section">
        <h2>Category Test</h2>
        <button onclick="testCategoryRetrieval()">Test Category Retrieval</button>
        <div id="category-test-result"></div>
    </div>

    <div class="test-section">
        <h2>Quick Access</h2>
        <button onclick="window.open('single.html', '_blank')">Open Main Comment Page</button>
        <button onclick="clearTestData()">Clear Test Data</button>
    </div>

    <div class="test-results" id="test-results"></div>

    <script src="js/supabase.min.js"></script>
    <script>
        // Supabase configuration
        const SUPABASE_URL = 'https://pghdjokgygdgqrykdypm.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBnaGRqb2tneWdkZ3FyeWtkeXBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5NzU2NDUsImV4cCI6MjA2MzU1MTY0NX0.8Guu5ZZv4RxJMw74kifDNj2F2-13k4SyecU3Fam2iN0';

        let supabaseClient;
        
        // Initialize Supabase client
        try {
            if (typeof Supabase !== 'undefined' && typeof Supabase.createClient === 'function') {
                supabaseClient = Supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
            } else if (typeof window.supabase !== 'undefined') {
                supabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
            } else {
                throw new Error("Supabase client not found");
            }
            console.log("Supabase client initialized successfully");
        } catch (error) {
            console.error("Failed to initialize Supabase client:", error);
        }

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function testDatabaseConnection() {
            try {
                showResult('db-test-result', 'Testing database connection...', 'info');
                
                const { data, error } = await supabaseClient
                    .from('comments')
                    .select('count', { count: 'exact', head: true });
                
                if (error) {
                    showResult('db-test-result', `Database connection failed: ${error.message}`, 'error');
                } else {
                    showResult('db-test-result', 'Database connection successful! ✅', 'success');
                }
            } catch (error) {
                showResult('db-test-result', `Connection error: ${error.message}`, 'error');
            }
        }

        async function testCommentInsertion() {
            try {
                showResult('comment-test-result', 'Testing comment insertion...', 'info');
                
                const testComment = {
                    name: 'Test User',
                    email: '<EMAIL>',
                    message: 'This is a test comment from the test page.',
                    post_id: 'test-post'
                };

                const { data, error } = await supabaseClient
                    .from('comments')
                    .insert([testComment])
                    .select();

                if (error) {
                    showResult('comment-test-result', `Comment insertion failed: ${error.message}`, 'error');
                } else {
                    showResult('comment-test-result', `Comment inserted successfully! ID: ${data[0].id} ✅`, 'success');
                }
            } catch (error) {
                showResult('comment-test-result', `Insertion error: ${error.message}`, 'error');
            }
        }

        async function testCommentRetrieval() {
            try {
                showResult('comment-test-result', 'Testing comment retrieval...', 'info');
                
                const { data, error } = await supabaseClient
                    .from('comments')
                    .select('*')
                    .limit(5);

                if (error) {
                    showResult('comment-test-result', `Comment retrieval failed: ${error.message}`, 'error');
                } else {
                    showResult('comment-test-result', `Retrieved ${data.length} comments successfully! ✅`, 'success');
                }
            } catch (error) {
                showResult('comment-test-result', `Retrieval error: ${error.message}`, 'error');
            }
        }

        async function testCategoryRetrieval() {
            try {
                showResult('category-test-result', 'Testing category retrieval...', 'info');
                
                const { data, error } = await supabaseClient
                    .from('categories')
                    .select('*');

                if (error) {
                    showResult('category-test-result', `Category retrieval failed: ${error.message}`, 'error');
                } else {
                    showResult('category-test-result', `Retrieved ${data.length} categories successfully! ✅`, 'success');
                }
            } catch (error) {
                showResult('category-test-result', `Category retrieval error: ${error.message}`, 'error');
            }
        }

        async function clearTestData() {
            if (confirm('Are you sure you want to clear test data? This will remove comments with post_id "test-post".')) {
                try {
                    const { error } = await supabaseClient
                        .from('comments')
                        .delete()
                        .eq('post_id', 'test-post');

                    if (error) {
                        alert(`Failed to clear test data: ${error.message}`);
                    } else {
                        alert('Test data cleared successfully!');
                    }
                } catch (error) {
                    alert(`Error clearing test data: ${error.message}`);
                }
            }
        }

        // Run initial connection test
        window.onload = function() {
            testDatabaseConnection();
        };
    </script>
</body>
</html>
