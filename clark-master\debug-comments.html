<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Comment System</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #output { white-space: pre-wrap; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Debug Comment System</h1>
    
    <div class="debug">
        <h3>Step 1: Test Supabase Connection</h3>
        <button onclick="testConnection()">Test Connection</button>
        <div id="connection-result"></div>
    </div>

    <div class="debug">
        <h3>Step 2: Test Comment Insertion</h3>
        <button onclick="testInsert()">Insert Test Comment</button>
        <div id="insert-result"></div>
    </div>

    <div class="debug">
        <h3>Step 3: Test Comment Retrieval</h3>
        <button onclick="testRetrieve()">Retrieve Comments</button>
        <div id="retrieve-result"></div>
    </div>

    <div class="debug">
        <h3>Debug Output</h3>
        <div id="output"></div>
    </div>

    <script src="js/supabase.min.js"></script>
    <script>
        const SUPABASE_URL = 'https://pghdjokgygdgqrykdypm.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBnaGRqb2tneWdkZ3FyeWtkeXBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5NzU2NDUsImV4cCI6MjA2MzU1MTY0NX0.8Guu5ZZv4RxJMw74kifDNj2F2-13k4SyecU3Fam2iN0';

        let supabaseClient;

        function log(message) {
            const output = document.getElementById('output');
            output.textContent += new Date().toISOString() + ': ' + message + '\n';
            console.log(message);
        }

        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${isError ? 'error' : 'success'}">${message}</div>`;
        }

        // Initialize Supabase
        try {
            log('Initializing Supabase client...');
            
            if (typeof Supabase !== 'undefined') {
                supabaseClient = Supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                log('Supabase client created successfully');
            } else {
                throw new Error('Supabase library not loaded');
            }
        } catch (error) {
            log('Error initializing Supabase: ' + error.message);
        }

        async function testConnection() {
            try {
                log('Testing database connection...');
                showResult('connection-result', 'Testing...', false);
                
                const { data, error, count } = await supabaseClient
                    .from('comments')
                    .select('*', { count: 'exact', head: true });
                
                if (error) {
                    log('Connection error: ' + JSON.stringify(error));
                    showResult('connection-result', 'Connection failed: ' + error.message, true);
                } else {
                    log('Connection successful. Comment count: ' + count);
                    showResult('connection-result', 'Connection successful! Found ' + count + ' comments.', false);
                }
            } catch (error) {
                log('Connection exception: ' + error.message);
                showResult('connection-result', 'Connection exception: ' + error.message, true);
            }
        }

        async function testInsert() {
            try {
                log('Testing comment insertion...');
                showResult('insert-result', 'Inserting...', false);
                
                const testComment = {
                    name: 'Debug Test User',
                    email: '<EMAIL>',
                    message: 'This is a debug test comment - ' + new Date().toISOString(),
                    post_id: 'debug-test'
                };
                
                log('Inserting comment: ' + JSON.stringify(testComment));
                
                const { data, error } = await supabaseClient
                    .from('comments')
                    .insert([testComment])
                    .select();
                
                if (error) {
                    log('Insert error: ' + JSON.stringify(error));
                    showResult('insert-result', 'Insert failed: ' + error.message, true);
                } else {
                    log('Insert successful: ' + JSON.stringify(data));
                    showResult('insert-result', 'Insert successful! Comment ID: ' + data[0].id, false);
                }
            } catch (error) {
                log('Insert exception: ' + error.message);
                showResult('insert-result', 'Insert exception: ' + error.message, true);
            }
        }

        async function testRetrieve() {
            try {
                log('Testing comment retrieval...');
                showResult('retrieve-result', 'Retrieving...', false);
                
                const { data, error } = await supabaseClient
                    .from('comments')
                    .select('*')
                    .order('created_at', { ascending: false })
                    .limit(5);
                
                if (error) {
                    log('Retrieve error: ' + JSON.stringify(error));
                    showResult('retrieve-result', 'Retrieve failed: ' + error.message, true);
                } else {
                    log('Retrieve successful: ' + JSON.stringify(data, null, 2));
                    showResult('retrieve-result', 'Retrieved ' + data.length + ' comments successfully!', false);
                }
            } catch (error) {
                log('Retrieve exception: ' + error.message);
                showResult('retrieve-result', 'Retrieve exception: ' + error.message, true);
            }
        }

        // Auto-test connection on load
        window.onload = function() {
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
